<template>
  <div>
    <div class="container-fluid">
      <div class="row">
        <!-- 答題卡側邊欄 - 桌面版 -->
        <div class="col-lg-3 d-none d-lg-block">
          <AnswerCard v-if="examDetail" :questions="examDetail.questions" :user-answers="examDetail.userAnswers"
            :current-index="currentQuestionIndex" :correct-answers="examDetail.questions.map(q => q.answer)"
            :chapter-title="examDetail?.chapter.title || ''" :current-language="examDetail?.languageText" mode="review"
            :enable-keyboard-events="true" @jump-to-question="jumpToQuestion" />

          <!-- 考試資訊卡片 -->
          <div class="card score-card mt-3">
            <div class="card-body">
              <div class="d-flex align-items-center gap-2">
                <span class="display-3 fw-bold">{{ examDetail.score }}分</span>
                <div>
                  <span class="badge bg-light text-dark fs-6 p-2">
                    ✔️ {{ examDetail.correctCount }}/{{ examDetail.totalQuestions }} 題
                  </span>
                </div>
              </div>

              <span class="fs-6">
                <i class="fas fa-calendar me-1"></i>
                {{ examDetail.date }}
              </span>
            </div>
          </div>
        </div>

        <!-- 主要內容區域 -->
        <div class="col-lg-9">
          <div class="exam-card h-100">
            <!-- 頂部導航區域 -->
            <div class="top-nav-area mb-3">
              <!-- 桌面版：返回按鈕 -->
              <div class="d-flex justify-content-between align-items-center">
                <BackBtn text="返回歷史記錄" @click="goBack" />
                <div class="d-inline d-sm-none">{{ examDetail?.languageText }}{{ examDetail?.chapter.title }}</div>
                <div class="text-muted">🔍答題回顧</div>
              </div>

              <!-- 手機版：章節標題 + 答題卡 -->
              <div class="d-lg-none mt-2" v-if="examDetail">
                <!-- 手機版答題卡 - 預設顯示 -->
                <div class="mobile-answer-card mb-3">
                  <div class="answer-scroll-container">
                    <div class="answer-grid-mobile-horizontal">
                      <button v-for="(_, index) in examDetail.questions" :key="index" class="answer-btn-mobile"
                        :class="getMobileButtonClass(index)" @click="jumpToQuestion(index)">
                        {{ index + 1 }}
                      </button>
                    </div>
                    <!-- 答題卡按鈕 -->
                    <button class="answer-card-btn" @click="openAnswerCardModal">
                      <i class="fas fa-th-large"></i>
                      <span>答題卡</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div v-if="!examDetail" class="text-center py-5">
              <h4 class="text-muted">找不到考試記錄</h4>
              <router-link to="/history" class="btn btn-gradient">返回歷史記錄</router-link>
            </div>

            <div v-else>
              <!-- 全部答對的情況 -->
              <div v-if="examDetail.incorrectQuestions.length === 0" class="alert alert-success text-center p-4">
                <i class="fas fa-trophy fs-1 mb-3 d-block"></i>
                <h4 class="fw-bold mb-2">🎊 恭喜！全部答對！</h4>
                <p class="mb-0">您的表現非常出色！</p>
              </div>

              <!-- 題目回顧 - 單題顯示 -->
              <div v-else>
                <!-- 進度指示器 -->
                <div class="text-center mb-3">
                  <h5 class="fw-semibold text-muted">
                    第 {{ currentQuestionIndex + 1 }} 題 / 共 {{ examDetail.questions.length }} 題
                  </h5>
                </div>

                <div class="progress mb-4" style="height: 8px;">
                  <div class="progress-bar bg-gradient" :style="{ width: progressPercentage + '%' }"></div>
                </div>

                <!-- 當前題目顯示 -->
                <div class="card border-0 bg-light" v-if="currentQuestion">
                  <div class="card-body p-4">
                    <!-- 答題狀態 -->
                    <div class="d-flex align-items-center justify-content-between mb-3">
                      <div class="d-flex align-items-center gap-2">
                        <span v-if="isAnswerCorrect(currentUserAnswer, currentQuestion.answer)"
                          class="badge bg-success fs-6">
                          <i class="fas fa-check me-1"></i>
                          答對
                        </span>
                        <span v-else class="badge bg-danger fs-6">
                          <i class="fas fa-times me-1"></i>
                          答錯
                        </span>
                        <span v-if="currentQuestion.type === 'multiple'" class="badge bg-info fs-6">
                          <i class="fas fa-check-double me-1"></i>
                          複選題
                        </span>
                        <span v-else class="badge bg-secondary fs-6">
                          <i class="fas fa-check me-1"></i>
                          單選題
                        </span>
                      </div>
                      <div class="text-muted">
                        <small>
                          我的答案：
                          <span v-if="currentUserAnswer === -1 || currentUserAnswer === null">
                            未作答
                          </span>
                          <span v-else-if="Array.isArray(currentUserAnswer)">
                            {{currentUserAnswer.map(ans => String.fromCharCode(65 + ans)).join(', ')}}
                          </span>
                          <span v-else>
                            {{ String.fromCharCode(65 + currentUserAnswer) }}
                          </span>
                        </small>
                      </div>
                    </div>

                    <h5 class="mb-4 card-text">
                      {{ currentQuestionIndex + 1 }}. {{ currentQuestion.q }}
                    </h5>

                    <!-- 選項 -->
                    <div class="d-grid gap-sm-3">
                      <div v-for="(option, optionIndex) in currentQuestion.options" :key="optionIndex"
                        class="option-item" :class="{
                          'user-answer': currentUserAnswer === optionIndex || (Array.isArray(currentUserAnswer) && currentUserAnswer.includes(optionIndex)),
                          'correct-answer': isCorrectAnswer(optionIndex, currentQuestion.answer)
                        }">
                        <div class="option-letter">{{ String.fromCharCode(65 + optionIndex) }}</div>
                        <div class="flex-grow-1">{{ option }}</div>
                        <div v-if="isCorrectAnswer(optionIndex, currentQuestion.answer)" class="option-indicator">
                          <i class="fas fa-check text-success"></i>
                        </div>
                      </div>
                    </div>

                    <!-- 正確答案區域 -->
                    <div class="correct-answer-section mt-3 pt-3 border-top">
                      <div class="correct-answer">
                        <strong>正確答案：</strong>
                        <span v-if="Array.isArray(currentQuestion.answer)">
                          {{currentQuestion.answer.map(ans => String.fromCharCode(65 + ans)).join(', ')}}
                        </span>
                        <span v-else>
                          {{ String.fromCharCode(65 + currentQuestion.answer) }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 導航按鈕 -->
                <div class="d-flex justify-content-between mt-4">
                  <button class="btn btn-secondary d-flex align-items-center gap-2" @click="prevQuestion"
                    :disabled="currentQuestionIndex === 0" v-show="currentQuestionIndex > 0">
                    <i class="fas fa-chevron-left"></i>
                    上一題
                  </button>

                  <div class="d-flex gap-3 flex-wrap">
                    <button class="btn btn-secondary d-flex align-items-center gap-2" @click="retakeExam">
                      <i class="fas fa-redo"></i>
                      重新測驗
                    </button>
                    <button v-if="!isFromHistory" class="btn btn-gradient d-flex align-items-center gap-2"
                      @click="goToChapters">
                      <i class="fas fa-list"></i>
                      返回章節選擇
                    </button>
                  </div>

                  <button class="btn btn-primary d-flex align-items-center gap-2" @click="nextQuestion"
                    :disabled="currentQuestionIndex >= examDetail.questions.length - 1">
                    <i class="fas fa-chevron-right"></i>
                    {{ currentQuestionIndex >= examDetail.questions.length - 1 ? '最後一題' : '下一題' }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 手機版答題卡 Modal -->
    <div class="modal fade" id="answerCardModal" tabindex="-1" aria-labelledby="answerCardModalLabel"
      aria-hidden="true">
      <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="answerCardModalLabel">
              <i class="fas fa-th-large me-2"></i>
              答題卡
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body p-0">
            <!-- 完整的桌面版答題卡 -->
            <AnswerCard v-if="examDetail" :questions="examDetail.questions" :user-answers="examDetail.userAnswers"
              :current-index="currentQuestionIndex" :correct-answers="examDetail.questions.map(q => q.answer)"
              mode="review" :enable-keyboard-events="false" @jump-to-question="jumpToQuestionFromModal" />
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">關閉</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useExamStore } from '../stores/examStore'
import AnswerCard from '../components/AnswerCard.vue'
import BackBtn from '../components/BackBtn.vue'

const router = useRouter()
const route = useRoute()
const examStore = useExamStore()

// 檢查登入狀態
if (!examStore.user.isLoggedIn) {
  router.push('/')
}

const currentQuestionIndex = ref(0)

// 判斷是否來自歷史記錄頁面
// 如果 URL 包含 from=history 查詢參數，或者 referrer 是歷史頁面，則認為是從歷史記錄進入
const isFromHistory = computed(() => {
  return route.query.from === 'history' ||
    (typeof document !== 'undefined' && document.referrer.includes('/history'))
})

const examDetail = computed(() => {
  const examId = route.params.id
  console.log('ExamDetailView - 查找考試記錄，ID:', examId)
  console.log('ExamDetailView - 當前歷史記錄:', examStore.examHistory)

  // 嘗試字符串匹配和數字匹配
  const found = examStore.examHistory.find(exam =>
    exam.id === examId ||
    exam.id === parseInt(examId) ||
    exam.id.toString() === examId
  )

  console.log('ExamDetailView - 找到的記錄:', found)
  return found
})

// 新增的 computed 屬性
const currentQuestion = computed(() => {
  if (!examDetail.value?.questions) return null
  return examDetail.value.questions[currentQuestionIndex.value]
})

const currentUserAnswer = computed(() => {
  if (!examDetail.value?.userAnswers) return null
  return examDetail.value.userAnswers[currentQuestionIndex.value]
})

const progressPercentage = computed(() => {
  if (!examDetail.value?.questions) return 0
  return ((currentQuestionIndex.value + 1) / examDetail.value.questions.length) * 100
})

// 導航函數
const jumpToQuestion = (index) => {
  currentQuestionIndex.value = index
}

const nextQuestion = () => {
  if (examDetail.value && currentQuestionIndex.value < examDetail.value.questions.length - 1) {
    currentQuestionIndex.value++
  }
}

const prevQuestion = () => {
  if (currentQuestionIndex.value > 0) {
    currentQuestionIndex.value--
  }
}

// 檢查選項是否為正確答案
const isCorrectAnswer = (optionIndex, correctAnswer) => {
  if (Array.isArray(correctAnswer)) {
    return correctAnswer.includes(optionIndex)
  }
  return optionIndex === correctAnswer
}

// 檢查用戶答案是否正確（用於答題狀態顯示）
const isAnswerCorrect = (userAnswer, correctAnswer) => {
  // 處理複選題
  if (Array.isArray(correctAnswer)) {
    if (!Array.isArray(userAnswer)) return false
    if (userAnswer.length !== correctAnswer.length) return false
    return userAnswer.every(ans => correctAnswer.includes(ans)) &&
      correctAnswer.every(ans => userAnswer.includes(ans))
  }
  // 處理單選題
  return userAnswer === correctAnswer
}

const retakeExam = async () => {
  if (examDetail.value?.chapter) {
    const success = await examStore.startExam(examDetail.value.chapter)
    if (success) {
      router.push(`/exam/${examDetail.value.chapter.id}`)
    }
  }
}

const goToChapters = () => {
  examStore.resetExam()
  examStore.selectLanguage(examDetail.value?.language || examStore.currentLanguage)
  router.push('/chapters')
}

const goBack = () => {
  router.push('/history')
}

const getMobileButtonClass = (index) => {
  const classes = []

  if (index === currentQuestionIndex.value) {
    classes.push('current')
  } else if (examDetail.value) {
    const userAnswer = examDetail.value.userAnswers[index]
    const correctAnswer = examDetail.value.questions[index].answer

    if (isAnswerCorrect(userAnswer, correctAnswer)) {
      classes.push('correct')
    } else {
      classes.push('incorrect')
    }
  }

  return classes
}

const openAnswerCardModal = () => {
  // 使用 Bootstrap 5 的 Modal API
  const modalElement = document.getElementById('answerCardModal')
  const modal = new window.bootstrap.Modal(modalElement)
  modal.show()
}

const jumpToQuestionFromModal = (index) => {
  currentQuestionIndex.value = index
  // 關閉 Modal
  const modalElement = document.getElementById('answerCardModal')
  const modal = window.bootstrap.Modal.getInstance(modalElement)
  if (modal) {
    modal.hide()
  }
}

// 鍵盤事件處理
const handleKeydown = (event) => {
  if (event.key === 'ArrowLeft') {
    event.preventDefault()
    prevQuestion()
  } else if (event.key === 'ArrowRight') {
    event.preventDefault()
    nextQuestion()
  }
}

onMounted(() => {
  console.log('ExamDetailView - onMounted 開始')
  console.log('ExamDetailView - examDetail:', examDetail.value)

  if (!examDetail.value) {
    console.log('ExamDetailView - 沒有找到考試記錄，跳轉到歷史頁面')
    router.push('/history')
    return
  }

  // 添加鍵盤事件監聽
  window.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  // 清理事件監聽器
  window.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.bg-gradient {
  background: linear-gradient(45deg, #667eea, #764ba2) !important;
}

.option-item {
  background: white;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  padding: 15px 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all 0.3s ease;
  cursor: default;
}

.option-item.user-answer {
  border-color: #17a2b8;
  background: #d1ecf1;
  color: #0c5460;
}

.option-item.correct-answer {
  border-color: #28a745;
  background: #d4edda;
  color: #155724;
}

.option-letter {
  font-weight: bold;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #e1e5e9;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.option-item.user-answer .option-letter {
  background: #17a2b8;
  color: white;
}

.option-item.correct-answer .option-letter {
  background: #28a745;
  color: white;
}

.correct-answer-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px 15px 0px 15px;
}

.correct-answer {
  color: #495057;
  font-size: 0.95rem;
}

.option-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-gradient {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border: none;
  color: white;
}

.btn-gradient:hover {
  background: linear-gradient(45deg, #5a6fd8, #6a4190);
  color: white;
}

.display-3 {
  font-size: 3rem !important;
}

.score-card {
  position: sticky;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  top: 96px;
}

/* 手機版答題卡樣式 */
.mobile-answer-card {
  background: #495057;
  border-radius: 12px;
  padding: 6px;
}

.answer-scroll-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.answer-grid-mobile-horizontal {
  display: flex;
  gap: 8px;
  overflow-x: auto;
  flex: 1;
  padding: 5px 0;
  /* 隱藏滾動條但保持滑動功能 */
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE and Edge */
}

.answer-grid-mobile-horizontal::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari, Opera */
}

.answer-btn-mobile {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: #6c757d;
  color: white;
  font-weight: bold;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  flex-shrink: 0;
}

.answer-btn-mobile:hover {
  transform: scale(1.1);
}

.answer-btn-mobile.correct {
  background: #56cc9d;
}

.answer-btn-mobile.incorrect {
  background: #ffadad;
  color: #e22b3d;
}

.answer-btn-mobile.current {
  background: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
}

/* 答題卡按鈕樣式 */
.answer-card-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: 2px solid #6c757d;
  border-radius: 8px;
  color: #6c757d;
  padding: 8px;
  height: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.answer-card-btn:hover {
  background: #6c757d;
  color: white;
}

.answer-card-btn i {
  font-size: 16px;
  margin-bottom: 2px;
}

.answer-card-btn span {
  font-size: 10px;
  font-weight: bold;
}

/* Modal 中的答題卡樣式 */
#answerCardModal .answer-card {
  position: static;
  top: auto;
}

#answerCardModal .card {
  border: none;
  box-shadow: none;
}

#answerCardModal .card-header {
  display: none;
  /* 隱藏標題，因為 Modal 已有標題 */
}

#answerCardModal .card-body {
  padding: 20px;
}

@media (max-width: 768px) {
  .question-review-card {
    padding: 20px 15px;
  }

  .option-review {
    padding: 12px 15px;
  }

  .display-3 {
    font-size: 2rem !important;
  }

  .d-flex.gap-3 {
    flex-direction: column;
  }
}
</style>
